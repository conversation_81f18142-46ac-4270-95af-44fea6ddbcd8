// ABOUTME: Works summary component - shows monthly stats for all Works with time tracking and activity overview  
// ABOUTME: Company-wide view displaying work details, ServiceContract relationships, and activity metrics

<template>
  <div class="works-summary">
    <div class="summary-container">
      <div class="summary-header">
        <div class="header-title-section">
          <h2 class="header-title">
            {{ $t('works_summary.title', 'Přehled prací') }}
          </h2>
          <div class="header-subtitle">{{ currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1) }}</div>
        </div>
        <div class="header-controls">
          <select :value="getCurrentMonthValue()" @change="onMonthSelect($event.target.value)" class="month-selector">
            <option value="" disabled selected>{{ $t('select_month', 'Vyberte měsíc') }}</option>
            <option v-for="month in availableMonths" 
                    :key="month.value" 
                    :value="month.value">
              {{ month.label }}
            </option>
          </select>
        </div>
      </div>

      <div v-if="isLoading" class="loading-container">
        <Clock :size="24" class="loading-icon" />
        <span>{{ $t('loading', 'Načítání...') }}</span>
      </div>

      <div v-else-if="summaryData && summaryData.length > 0" class="summary-table-container">
        <div class="summary-table">
          <div class="table-header">
            <div class="header-cell">{{ $t('work_title', 'Název práce') }}</div>
            <div class="header-cell">{{ $t('service_contract', 'Zakázka') }}</div>
            <div class="header-cell header-cell--center">{{ $t('status', 'Stav') }}</div>
            <div class="header-cell header-cell--number">{{ $t('total_time', 'Celkový čas') }}</div>
            <div class="header-cell header-cell--number">{{ $t('sessions_count', 'Sezení') }}</div>
            <div class="header-cell header-cell--number">{{ $t('activities_count', 'Aktivity') }}</div>
            <div class="header-cell">{{ $t('last_activity', 'Poslední aktivita') }}</div>
          </div>

          <div 
            v-for="work in summaryData" 
            :key="work.id"
            class="table-row"
          >
            <div class="table-cell work-cell">
              <div class="work-title">{{ work.title }}</div>
            </div>
            <div class="table-cell contract-cell">
              <div class="contract-title">{{ work.service_contract_title }}</div>
            </div>
            <div class="table-cell table-cell--center">
              <span class="status-badge" :class="`status-${work.status}`">
                {{ $t(`status.${work.status}`, work.status) }}
              </span>
            </div>
            <div class="table-cell table-cell--number">
              {{ formatHours(work.total_time_seconds) }}
            </div>
            <div class="table-cell table-cell--number">
              {{ work.work_sessions_count || 0 }}
            </div>
            <div class="table-cell table-cell--number">
              {{ work.daily_activities_count || 0 }}
            </div>
            <div class="table-cell">
              {{ work.last_activity ? formatDate(work.last_activity) : '-' }}
            </div>
          </div>
        </div>

        <div class="summary-stats">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">{{ $t('total_works', 'Celkem prací') }}:</span>
              <span class="stat-value">{{ summaryStats.total_works || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{{ $t('total_time_spent', 'Celkem času') }}:</span>
              <span class="stat-value">{{ formatHours(summaryStats.total_time_seconds || 0) }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{{ $t('total_sessions', 'Celkem sezení') }}:</span>
              <span class="stat-value">{{ summaryStats.total_work_sessions || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{{ $t('total_activities', 'Celkem aktivit') }}:</span>
              <span class="stat-value">{{ summaryStats.total_activities || 0 }}</span>
            </div>
          </div>

          <!-- Status Distribution -->
          <div v-if="statusDistribution && Object.keys(statusDistribution).length > 0" class="status-distribution">
            <h4 class="distribution-title">{{ $t('status_distribution', 'Rozložení stavů') }}</h4>
            <div class="status-items">
              <div 
                v-for="[status, count] in Object.entries(statusDistribution)" 
                :key="status"
                class="status-item"
              >
                <span class="status-badge" :class="`status-${status}`">
                  {{ $t(`status.${status}`, status) }}
                </span>
                <span class="status-count">{{ count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <p>{{ $t('works_summary.no_data', 'Žádná data pro vybraný měsíc') }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { Clock } from 'lucide-vue-next';
import { sendFlashMessage } from '@/utils/flashMessage';
import dayjs from 'dayjs';

export default {
  name: 'WorksSummary',
  components: {
    Clock
  },
  data() {
    // Get persisted date or default to current month
    const persistedDate = localStorage.getItem('worksSummary_lastViewedMonth');
    const selectedDate = persistedDate ? dayjs(persistedDate) : dayjs();
    
    return {
      summaryData: [],
      summaryStats: {},
      isLoading: false,
      currentMonth: '',
      availableMonths: [],
      currentYear: selectedDate.year(),
      currentMonthNumber: selectedDate.month() + 1,
      companyName: ''
    };
  },
  computed: {
    statusDistribution() {
      return this.summaryStats.status_distribution || {};
    }
  },
  async mounted() {
    this.initializeMonths();
    await this.fetchSummaryData(this.currentYear, this.currentMonthNumber);
  },
  methods: {
    async fetchSummaryData(year, month) {
      this.isLoading = true;
      
      try {
        const params = { year, month };
        console.log('🔍 DEBUG Works API Call parameters:', params);
        
        const response = await axios.get('/api/v1/daily_logs/works_summary', {
          params: params,
          headers: { 'Accept': 'application/json' }
        });
        
        console.log('🔍 DEBUG Works API Response:', response.data);
        
        this.summaryData = response.data.works || [];
        this.summaryStats = response.data.summary_stats || {};
        this.companyName = response.data.company_name || '';
        this.currentYear = year;
        this.currentMonthNumber = month;
        this.updateCurrentMonth(year, month);
        
      } catch (error) {
        console.error('Error fetching works summary data:', error);
        if (error.response?.status === 403) {
          sendFlashMessage(
            this.$t('unauthorized_access', 'Nemáte oprávnění zobrazit tyto údaje'),
            'error'
          );
        } else {
          sendFlashMessage(
            error.response?.data?.message || this.$t('works_summary.error_loading', 'Chyba při načítání přehledu'),
            'error'
          );
        }
      } finally {
        this.isLoading = false;
      }
    },
    
    initializeMonths() {
      const months = [];
      let date = dayjs();
      
      // Show last 12 months using dayjs
      for(let i = 0; i < 12; i++) {
        months.unshift({
          value: `${date.year()}-${date.month() + 1}`,
          label: date.toDate().toLocaleString(this.$i18n.locale, { month: 'long', year: 'numeric' })
        });
        date = date.subtract(1, 'month');
      }
      
      this.availableMonths = months;
    },
    
    getCurrentMonthValue() {
      if (!this.currentYear || !this.currentMonthNumber) {
        const now = dayjs();
        return `${now.year()}-${now.month() + 1}`;
      }
      return `${this.currentYear}-${this.currentMonthNumber}`;
    },
    
    async onMonthSelect(value) {
      if (!value) return;
      
      const [year, month] = value.split('-').map(Number);
      
      // Persist selected month to localStorage
      const selectedDate = dayjs().year(year).month(month - 1).startOf('month');
      localStorage.setItem('worksSummary_lastViewedMonth', selectedDate.toISOString());
      
      await this.fetchSummaryData(year, month);
    },
    
    updateCurrentMonth(year, month) {
      const date = dayjs().year(year).month(month - 1).date(1);
      this.currentMonth = date.toDate().toLocaleDateString(this.$i18n.locale, { month: 'long', year: 'numeric' });
    },
    
    formatHours(totalSeconds) {
      if (!totalSeconds) return '0:00';
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.round((totalSeconds % 3600) / 60);
      return `${hours}:${minutes.toString().padStart(2, '0')}`;
    },
    
    formatDate(dateString) {
      if (!dateString) return '-';
      return dayjs(dateString).format('DD.MM.YYYY');
    }
  }
};
</script>

<style scoped>
.works-summary {
  padding: 1rem;
  min-height: 100vh;
}

.summary-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  border-bottom: 2px solid #e5e7eb;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-title-section {
  flex: 1;
  min-width: 250px;
}

.header-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
}

.header-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.header-controls {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.month-selector {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  min-width: 180px;
}

.month-selector:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 0.5rem;
  color: #6b7280;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.summary-table-container {
  padding: 0 1.5rem 1.5rem;
}

.summary-table {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1fr 1.5fr;
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  border-bottom: 2px solid #e5e7eb;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1fr 1.5fr;
  border-bottom: 1px solid #f3f4f6;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: #f9fafb;
}

.header-cell,
.table-cell {
  padding: 0.75rem;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.header-cell--center,
.table-cell--center {
  justify-content: center;
  text-align: center;
}

.header-cell--number,
.table-cell--number {
  justify-content: center;
  text-align: center;
}

.work-cell,
.contract-cell {
  padding-left: 1rem;
}

.work-title,
.contract-title {
  font-weight: 500;
  color: #1f2937;
}

.contract-title {
  font-size: 0.85rem;
  color: #6b7280;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-scheduled {
  background-color: #ddd6fe;
  color: #6d28d9;
}

.status-in_progress {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

.status-unprocessed {
  background-color: #f3f4f6;
  color: #6b7280;
}

.status-rescheduled {
  background-color: #fef3c7;
  color: #d97706;
}

.summary-stats {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #374151;
}

.stat-label {
  font-weight: 500;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
}

.status-distribution {
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

.distribution-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.status-items {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-count {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
}

@media (max-width: 1200px) {
  .table-header,
  .table-row {
    grid-template-columns: 1.5fr 1.5fr 0.8fr 0.8fr 0.8fr 0.8fr 1fr;
    font-size: 0.85rem;
  }
  
  .header-cell,
  .table-cell {
    padding: 0.6rem 0.5rem;
  }
}

@media (max-width: 968px) {
  .summary-header {
    flex-direction: column;
    align-items: stretch;
    padding: 1rem;
  }
  
  .header-controls {
    justify-content: flex-start;
    margin-top: 0.5rem;
  }
  
  .month-selector {
    width: 100%;
    max-width: 200px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1.2fr 1fr 0.7fr 0.7fr 0.7fr 0.7fr;
    font-size: 0.8rem;
  }
  
  .header-cell:last-child,
  .table-cell:last-child {
    display: none;
  }
  
  .header-cell,
  .table-cell {
    padding: 0.5rem 0.4rem;
  }
  
  .summary-table-container {
    padding: 0 1rem 1rem;
  }
  
  .status-items {
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .works-summary {
    padding: 0.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }
  
  .status-items {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
