# CRITICAL FIX: Owner Gets Logged Out When Employee Leaves Company

## Problem
When any employee left a company, the company owner (and all other users) would get logged out upon page refresh. This was a critical authentication bug affecting multi-tenant functionality.

## Root Cause
In `app/models/user.rb`, the `leave_company` method had a critical bug on line 117:

```ruby
# BEFORE (BUG):
company_user_roles.unscoped.where(company: company).update_all(is_primary: false)
```

This query was removing the `is_primary` flag from ALL users in the company, not just the leaving user. This caused:
1. All users to lose their primary company designation
2. JWT token validation failures 
3. Users being unable to restore their sessions
4. Forced logouts for everyone in the company

## Solution
Changed the query to only affect the leaving user's roles:

```ruby
# AFTER (FIXED):
CompanyUserRole.where(user_id: self.id, company: company).update_all(is_primary: false)
```

## File Changed
- `app/models/user.rb` - Line 118 (previously 117)

## Test Coverage
Added comprehensive tests:
- `spec/models/user_leave_company_spec.rb` - Unit tests for the leave_company method
- `spec/features/employee_leave_doesnt_logout_owner_spec.rb` - Integration test verifying owner remains logged in

## Impact
This fix ensures:
- When an employee leaves a company, only their role is deactivated
- The owner and other users maintain their `is_primary` flag
- No one gets unexpectedly logged out
- JWT tokens remain valid for all other users
- Multi-tenant authentication remains intact

## Verification
The logs showed the problematic SQL query:
```sql
UPDATE "company_user_roles" SET "is_primary" = false WHERE "company_user_roles"."company_id" = 2
```

After the fix, the query correctly includes the user_id:
```sql
UPDATE "company_user_roles" SET "is_primary" = false WHERE "company_user_roles"."user_id" = 25 AND "company_user_roles"."company_id" = 2
```

## Date Fixed
2025-08-24